# Contracts and Shifts API Documentation

This document describes the newly implemented Contracts and Shifts functionality for the Laravel API.

## Overview

The system implements a contract management system where:
- **Users** can have multiple contracts
- **Contracts** are linked to specific shifts and users
- **Shifts** define working hours and days of the week
- Contracts can be created, updated, ended, and renewed

## Database Schema

### Users Table
- Updated to use UUID primary keys
- Maintains existing fields: name, email, password, provider, provider_id

### Shifts Table
- `id` (UUID, Primary Key)
- `start` (TIME) - Start time in HH:MM format
- `end` (TIME) - End time in HH:MM format
- `days_of_week` (JSON) - Array of days like ['monday', 'tuesday', 'wednesday']
- `created_at`, `updated_at`, `deleted_at` (timestamps)

### Contracts Table
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key to users)
- `start_date` (DATE) - Contract start date
- `end_date` (DATE) - Contract end date
- `monthly_salary` (INTEGER) - Monthly salary amount
- `shift_id` (UUID, Foreign Key to shifts)
- `pharmacy_id` (UUID, nullable) - Will be linked when pharmacies are implemented
- `created_at`, `updated_at`, `deleted_at` (timestamps)

## API Endpoints

### Authentication
All endpoints require authentication using Bearer token in the Authorization header.

### Shifts API

#### GET /api/shifts
Get all shifts with pagination and filtering.

**Query Parameters:**
- `page` (int) - Page number (default: 1)
- `perPage` (int) - Items per page (default: 15)
- `search` (string) - Search in start/end times
- `days` (string) - Comma-separated list of days to filter by
- `startTime` (string) - Filter by minimum start time
- `endTime` (string) - Filter by maximum end time

#### POST /api/shifts
Create a new shift.

**Request Body:**
```json
{
  "start": "09:00",
  "end": "17:00",
  "daysOfWeek": ["monday", "tuesday", "wednesday", "thursday", "friday"]
}
```

#### GET /api/shifts/{id}
Get a specific shift by ID.

#### PUT /api/shifts/{id}
Update a specific shift.

#### DELETE /api/shifts/{id}
Delete a specific shift (only if not used by any contracts).

#### GET /api/shifts/by-days?days=monday,tuesday
Get shifts filtered by specific days.

#### GET /api/shifts/available
Get shifts not currently assigned to any active contracts.

### Contracts API

#### GET /api/contracts
Get all contracts with pagination and filtering.

**Query Parameters:**
- `page` (int) - Page number
- `perPage` (int) - Items per page
- `search` (string) - Search in user name/email
- `userId` (UUID) - Filter by user
- `shiftId` (UUID) - Filter by shift
- `status` (string) - Filter by status: 'active', 'expired', 'future', 'ending_soon'
- `startDateFrom`, `startDateTo` - Filter by start date range
- `endDateFrom`, `endDateTo` - Filter by end date range
- `salaryMin`, `salaryMax` - Filter by salary range
- `endingSoonDays` (int) - Days threshold for ending soon filter

#### POST /api/contracts
Create a new contract.

**Request Body:**
```json
{
  "userId": "uuid",
  "startDate": "2025-01-01",
  "endDate": "2025-12-31",
  "monthlySalary": 50000,
  "shiftId": "uuid",
  "pharmacyId": "uuid" // optional
}
```

#### GET /api/contracts/{id}
Get a specific contract by ID.

#### PUT /api/contracts/{id}
Update a specific contract.

#### DELETE /api/contracts/{id}
Delete a specific contract.

#### PATCH /api/contracts/{id}/end
End a contract early.

**Request Body:**
```json
{
  "endDate": "2025-06-30" // optional, defaults to today
}
```

#### POST /api/contracts/{id}/renew
Renew a contract (creates a new contract starting after the current one ends).

**Request Body:**
```json
{
  "endDate": "2026-12-31",
  "monthlySalary": 55000, // optional, defaults to current salary
  "shiftId": "uuid", // optional, defaults to current shift
  "pharmacyId": "uuid" // optional
}
```

#### GET /api/contracts/active
Get all currently active contracts.

#### GET /api/contracts/expired
Get all expired contracts.

#### GET /api/contracts/ending-soon?days=30
Get contracts ending within specified days (default: 30).

#### GET /api/contracts/user/{userId}
Get all contracts for a specific user.

## Response Format

All responses follow the established camelCase format using Resource classes:

### Shift Resource
```json
{
  "id": "uuid",
  "start": "09:00",
  "end": "17:00",
  "daysOfWeek": ["monday", "tuesday", "wednesday", "thursday", "friday"],
  "createdAt": "2025-01-01T00:00:00.000000Z",
  "updatedAt": "2025-01-01T00:00:00.000000Z",
  "contractsCount": 5
}
```

### Contract Resource
```json
{
  "id": "uuid",
  "userId": "uuid",
  "startDate": "2025-01-01",
  "endDate": "2025-12-31",
  "monthlySalary": 50000,
  "shiftId": "uuid",
  "pharmacyId": null,
  "createdAt": "2025-01-01T00:00:00.000000Z",
  "updatedAt": "2025-01-01T00:00:00.000000Z",
  "isActive": true,
  "isExpired": false,
  "isFuture": false,
  "durationInDays": 365,
  "durationInMonths": 12,
  "remainingDays": 300,
  "user": { /* UserResource */ },
  "shift": { /* ShiftResource */ }
}
```

## Validation Rules

### Shifts
- `start`: Required, valid time format (HH:MM)
- `end`: Required, valid time format (HH:MM), must be after start time
- `daysOfWeek`: Required array with at least one valid day

### Contracts
- `userId`: Required, valid UUID, must exist in users table
- `startDate`: Required, valid date, must be today or future (for new contracts)
- `endDate`: Required, valid date, must be after start date
- `monthlySalary`: Required, integer, minimum 1
- `shiftId`: Required, valid UUID, must exist in shifts table
- `pharmacyId`: Optional, valid UUID (validation for existence will be added when pharmacies are implemented)

## Testing

Comprehensive test suites have been implemented:
- `tests/Feature/ShiftApiTest.php` - Tests all shift endpoints
- `tests/Feature/ContractApiTest.php` - Tests all contract endpoints

Run tests with:
```bash
php artisan test tests/Feature/ShiftApiTest.php
php artisan test tests/Feature/ContractApiTest.php
```

## Seeding

Sample data can be generated using:
```bash
php artisan db:seed
```

This will create:
- Test users
- Various shift patterns (morning, evening, weekend, etc.)
- Sample contracts in different states (active, expired, future, ending soon)

## Future Enhancements

When pharmacies are implemented:
1. Add foreign key constraint for `pharmacy_id` in contracts table
2. Update validation rules to check pharmacy existence
3. Add pharmacy relationship to Contract resource
4. Update contract filtering to include pharmacy-based filters
