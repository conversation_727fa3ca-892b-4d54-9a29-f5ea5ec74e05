<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

class EndContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'endDate' => 'nullable|date|after_or_equal:today',
        ];
    }

    public function validatedData(): array
    {
        $validated = $this->validated();

        return [
            'end_date' => $validated['endDate'] ?? null,
        ];
    }
}
