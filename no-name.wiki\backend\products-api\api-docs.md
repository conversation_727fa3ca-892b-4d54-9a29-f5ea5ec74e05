# Products API Documentation

This document provides detailed information about the Products API endpoints available in the application.

## Authentication

All endpoints require authentication. Include the authentication token in the request header:

```
Authorization: Bearer {your_token}
```

## Response Format

All responses are in JSON format with camelCase property names. Paginated responses include pagination metadata.

## Endpoints

### 1. List All Products

Retrieves a paginated list of products with optional filtering.

- **URL**: `/api/products`
- **Method**: `GET`
- **Auth Required**: Yes

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | integer | No | Page number (default: 1) |
| perPage | integer | No | Items per page (default: 15) |
| search | string | No | Search term for product name or active ingredient |
| categoryId | uuid | No | Filter products by category ID |
| expirationStatus | string | No | Filter by expiration status: 'expired' or 'expiring_soon' |
| months | integer | No | Number of months for expiring soon filter (default: 3) |
| sortByExpiration | boolean | No | Sort products by expiration date (default: false) |

#### Success Response

```json
{
  "data": [
    {
      "id": "uuid-string",
      "name": "Product Name",
      "activeIngredient": "Active Ingredient",
      "description": "Product description",
      "shape": "Tablet",
      "expDate": "2023-12-31",
      "categoryId": "category-uuid",
      "quantity": 100,
      "createdAt": "2023-01-01T00:00:00.000000Z",
      "updatedAt": "2023-01-01T00:00:00.000000Z"
    },
    // More products...
  ],
  "links": {
    "first": "http://example.com/api/products?page=1",
    "last": "http://example.com/api/products?page=5",
    "prev": null,
    "next": "http://example.com/api/products?page=2"
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 5,
    "path": "http://example.com/api/products",
    "per_page": 15,
    "to": 15,
    "total": 75
  },
  "page": 1,
  "perPage": 15
}
```

### 2. Get Product by ID

Retrieves a specific product by its ID.

- **URL**: `/api/products/{id}`
- **Method**: `GET`
- **Auth Required**: Yes

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | uuid | Yes | Product ID |

#### Success Response

```json
{
  "data": {
    "id": "uuid-string",
    "name": "Product Name",
    "activeIngredient": "Active Ingredient",
    "description": "Product description",
    "shape": "Tablet",
    "expDate": "2023-12-31",
    "categoryId": "category-uuid",
    "quantity": 100,
    "createdAt": "2023-01-01T00:00:00.000000Z",
    "updatedAt": "2023-01-01T00:00:00.000000Z"
  }
}
```

#### Error Response

```json
{
  "message": "product not found"
}
```

### 3. Create Product

Creates a new product.

- **URL**: `/api/products`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### Request Body

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | Yes | Product name (max: 255 chars) |
| activeIngredient | string | Yes | Active ingredient (max: 255 chars) |
| description | string | No | Product description |
| shape | string | Yes | Product shape (max: 255 chars) |
| expDate | date | Yes | Expiration date (must be after today) |
| categoryId | uuid | Yes | Category ID (must exist in categories table) |
| quantity | integer | No | Initial stock quantity (min: 0) |

#### Success Response

```json
{
  "data": {
    "id": "uuid-string",
    "name": "Product Name",
    "activeIngredient": "Active Ingredient",
    "description": "Product description",
    "shape": "Tablet",
    "expDate": "2023-12-31",
    "categoryId": "category-uuid",
    "quantity": 100,
    "createdAt": "2023-01-01T00:00:00.000000Z",
    "updatedAt": "2023-01-01T00:00:00.000000Z"
  },
  "message": "product created successfully"
}
```

### 4. Update Product

Updates an existing product.

- **URL**: `/api/products/{id}`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | uuid | Yes | Product ID |

#### Request Body

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | No | Product name (max: 255 chars) |
| activeIngredient | string | No | Active ingredient (max: 255 chars) |
| description | string | No | Product description |
| shape | string | No | Product shape (max: 255 chars) |
| expDate | date | No | Expiration date |
| categoryId | uuid | No | Category ID (must exist in categories table) |
| quantity | integer | No | Stock quantity (min: 0) |

#### Success Response

```json
{
  "data": {
    "id": "uuid-string",
    "name": "Updated Product Name",
    "activeIngredient": "Updated Active Ingredient",
    "description": "Updated product description",
    "shape": "Capsule",
    "expDate": "2024-12-31",
    "categoryId": "category-uuid",
    "quantity": 150,
    "createdAt": "2023-01-01T00:00:00.000000Z",
    "updatedAt": "2023-01-02T00:00:00.000000Z"
  },
  "message": "product updated successfully"
}
```

#### Error Response

```json
{
  "message": "product not found"
}
```

### 5. Delete Product

Deletes a product.

- **URL**: `/api/products/{id}`
- **Method**: `DELETE`
- **Auth Required**: Yes

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | uuid | Yes | Product ID |

#### Success Response

- Status Code: 204 No Content

#### Error Response

```json
{
  "message": "product not found"
}
```

### 6. Get Expired Products

Retrieves a paginated list of expired products.

- **URL**: `/api/products/expired`
- **Method**: `GET`
- **Auth Required**: Yes

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | integer | No | Page number (default: 1) |
| perPage | integer | No | Items per page (default: 15) |

#### Success Response

Similar to the List All Products response, but only includes expired products.

### 7. Get Expiring Soon Products

Retrieves a paginated list of products that will expire soon.

- **URL**: `/api/products/expiring-soon`
- **Method**: `GET`
- **Auth Required**: Yes

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | integer | No | Page number (default: 1) |
| perPage | integer | No | Items per page (default: 15) |
| months | integer | No | Number of months to consider for "expiring soon" (default: 3) |

#### Success Response

Similar to the List All Products response, but only includes products expiring within the specified months.

### 8. Search Products

Searches for products by name or active ingredient.

- **URL**: `/api/products/search`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### Request Body

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| term | string | Yes | Search term (min: 2 chars) |

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | integer | No | Page number (default: 1) |
| perPage | integer | No | Items per page (default: 15) |

#### Success Response

Similar to the List All Products response, but only includes products matching the search term.

### 9. Update Product Stocks

Updates the stock quantities for multiple products in a single request.

- **URL**: `/api/products/stocks/update`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### Request Body

```json
{
  "stocks": [
    {
      "productId": "uuid-string-1",
      "quantity": 10
    },
    {
      "productId": "uuid-string-2",
      "quantity": 20
    }
  ]
}
```

#### Success Response

```json
{
  "message": "products stocks updated successfully",
  "data": {
    "success": [
      {
        "productId": "uuid-string-1",
        "productName": "Product 1",
        "oldQuantity": 90,
        "newQuantity": 100
      },
      {
        "productId": "uuid-string-2",
        "productName": "Product 2",
        "oldQuantity": 30,
        "newQuantity": 50
      }
    ],
    "failed": []
  }
}
```

## Error Handling

All endpoints may return the following error responses:

- **401 Unauthorized**: If the authentication token is missing or invalid
- **422 Unprocessable Entity**: If the request data fails validation
- **404 Not Found**: If the requested resource is not found
- **500 Internal Server Error**: If there's a server error

## Pagination

Paginated responses include the following metadata:

- `links`: Contains URLs for first, last, previous, and next pages
- `meta`: Contains pagination information like current page, total pages, etc.
- `page`: Current page number
- `perPage`: Number of items per page
