modify the products update stocks endpoint, currently it tries to update each product, if it can update it then it updates it, if it can't update it then it discards the update, the response has the successfully updated products and the failed to update products, make it so if one failed to update then nothing gets updated 

check if user<PERSON>ontroll<PERSON> is still working after lody merges, cause i modified the pk to be uuid instead of auto increment integer